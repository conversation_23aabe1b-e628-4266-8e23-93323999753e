package com.chic.dea.apis.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 提数任务查询请求DTO
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class TaskQueryRequest {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * OA ID
     */
    private String oaid;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 提取状态
     */
    private String extractionStatus;

    /**
     * 数据源ID
     */
    private Long dataSourceId;

    /**
     * 是否归档
     */
    private Integer isArchived;

    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;

    /**
     * 执行时间开始
     */
    private LocalDateTime executionTimeStart;

    /**
     * 执行时间结束
     */
    private LocalDateTime executionTimeEnd;
}
