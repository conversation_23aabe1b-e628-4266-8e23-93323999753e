package com.chic.dea.domain.database.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 提数任务实体类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@TableName("extraction_task")
public class ExtractionTask {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * OA ID
     */
    @TableField("oaid")
    private String oaid;

    /**
     * 任务标题
     */
    @TableField("title")
    private String title;

    /**
     * 申请人
     */
    @TableField("applicant")
    private String applicant;

    /**
     * 申请人邮箱
     */
    @TableField("applicant_email")
    private String applicantEmail;

    /**
     * 申请人手机
     */
    @TableField("applicant_phone")
    private String applicantPhone;

    /**
     * 提取状态
     */
    @TableField("extraction_status")
    private String extractionStatus;

    /**
     * 提取脚本
     */
    @TableField("extraction_script")
    private String extractionScript;

    /**
     * 数据源ID
     */
    @TableField("data_source_id")
    private Long dataSourceId;

    /**
     * 合规OA文件URL
     */
    @TableField("compliance_pdf_url")
    private String compliancePdfUrl;

    /**
     * 样例数据URL
     */
    @TableField("sample_data_url")
    private String sampleDataUrl;

    /**
     * 结果文件URL
     */
    @TableField("result_file_url")
    private String resultFileUrl;

    /**
     * 文件密码
     */
    @TableField("file_password")
    private String filePassword;

    /**
     * 总记录数
     */
    @TableField("total_records")
    private Long totalRecords;

    /**
     * 文件大小
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 执行开始时间
     */
    @TableField("execution_start_time")
    private LocalDateTime executionStartTime;

    /**
     * 执行结束时间
     */
    @TableField("execution_end_time")
    private LocalDateTime executionEndTime;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 是否归档
     */
    @TableField("is_archived")
    private Integer isArchived;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人名称
     */
    @TableField("create_by_name")
    private String createByName;

    /**
     * 修改人名称
     */
    @TableField("update_by_name")
    private String updateByName;
    /**
     * 每个文件的最大记录数
     */
    @TableField("max_records_per_file")
    private Integer maxRecordsPerFile = 700000;

    /**
     * 获取提取状态枚举
     */
    public ExtractionStatus getExtractionStatusEnum() {
        if (extractionStatus == null) {
            return null;
        }
        return ExtractionStatus.valueOf(extractionStatus);
    }

    /**
     * 设置提取状态枚举
     */
    public void setExtractionStatusEnum(ExtractionStatus status) {
        this.extractionStatus = status != null ? status.name() : null;
    }

    /**
     * 检查任务是否可以编辑
     */
    public boolean isEditable() {
        ExtractionStatus status = getExtractionStatusEnum();
        return status != null && status.isEditable();
    }

    /**
     * 检查任务是否可以执行
     */
    public boolean isExecutable() {
        ExtractionStatus status = getExtractionStatusEnum();
        return status != null && status.isExecutable();
    }

    /**
     * 计算执行时长（毫秒）
     */
    public Long getExecutionDuration() {
        if (executionStartTime != null && executionEndTime != null) {
            return java.time.Duration.between(executionStartTime, executionEndTime).toMillis();
        }
        return null;
    }
}
