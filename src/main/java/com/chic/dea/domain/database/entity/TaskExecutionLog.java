package com.chic.dea.domain.database.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务执行日志实体类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@TableName("task_execution_log")
public class TaskExecutionLog {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 步骤名称
     */
    @TableField("step_name")
    private String stepName;

    /**
     * 执行状态
     */
    @TableField("status")
    private String status;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 执行上下文
     */
    @TableField("execution_context")
    private String executionContext;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人名称
     */
    @TableField("create_by_name")
    private String createByName;

    /**
     * 计算执行时长（毫秒）
     */
    public Long getExecutionDuration() {
        if (startTime != null && endTime != null) {
            return java.time.Duration.between(startTime, endTime).toMillis();
        }
        return null;
    }

    /**
     * 检查是否执行成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(status);
    }

    /**
     * 检查是否执行失败
     */
    public boolean isFailed() {
        return "FAILED".equals(status);
    }

    /**
     * 创建成功日志
     */
    public static TaskExecutionLog createSuccessLog(Long taskId, String stepName, LocalDateTime startTime, LocalDateTime endTime) {
        TaskExecutionLog log = new TaskExecutionLog();
        log.setTaskId(taskId);
        log.setStepName(stepName);
        log.setStatus("SUCCESS");
        log.setStartTime(startTime);
        log.setEndTime(endTime);
        return log;
    }

    /**
     * 创建失败日志
     */
    public static TaskExecutionLog createFailedLog(Long taskId, String stepName, LocalDateTime startTime, String errorMessage) {
        TaskExecutionLog log = new TaskExecutionLog();
        log.setTaskId(taskId);
        log.setStepName(stepName);
        log.setStatus("FAILED");
        log.setStartTime(startTime);
        log.setEndTime(LocalDateTime.now());
        log.setErrorMessage(errorMessage);
        return log;
    }
}
