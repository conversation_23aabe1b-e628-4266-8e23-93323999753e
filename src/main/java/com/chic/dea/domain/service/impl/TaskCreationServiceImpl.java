package com.chic.dea.domain.service.impl;

import com.chic.dea.apis.model.dto.*;
import com.chic.dea.domain.database.entity.*;
import com.chic.dea.domain.database.mapper.*;
import com.chic.dea.domain.service.*;
import com.chic.dea.domain.service.SqlValidationService.*;
import com.chic.dea.domain.service.SensitiveFieldService.*;
import com.chic.dea.infrastructure.sql.SqlBuilder;
import com.chic.dea.infrastructure.sql.SqlBuilderContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 任务创建服务实现类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskCreationServiceImpl implements TaskCreationService {

    private final ExtractionTaskMapper taskMapper;
    private final TaskExecutionQueueMapper queueMapper;
    private final DataSourceService dataSourceService;
    private final SqlValidationService sqlValidationService;
    private final SensitiveFieldService sensitiveFieldService;
    private final SqlLineageService sqlLineageService;
    private final DataSourceManager dataSourceManager;



    @Override
    public SqlValidationResult validateSQL(String sql, Long dataSourceId) {
        log.info("校验SQL脚本, dataSourceId: {}", dataSourceId);

        // 获取数据源信息
        DataSource dataSource = dataSourceService.getDataSourceEntityById(dataSourceId);
        String databaseType = dataSource.getType();

        // 使用Apache Calcite进行SQL语法校验
        SqlValidationResult syntaxResult = sqlValidationService.validateSyntax(sql, databaseType);
        if (!syntaxResult.isValid()) {
            log.error("SQL语法校验失败: {}", syntaxResult.getErrorMessage());
        }
        return syntaxResult;
    }

    private List<SensitiveFieldCheckResult> checkSensitiveFields(String sql, String databaseType) {
        // 解析SQL获取涉及的表和字段
        String viewSql = sqlValidationService.createViewClause(sql);
        log.info("生成视图语句: {}", viewSql);
        // 调用SQL血缘服务，获取血缘关系
        List<LineageTableFieldsVO> lineageTableFieldsVOS = sqlLineageService.extractTableFields(viewSql, databaseType.toLowerCase());

        // 敏感字段检查(调用天御平台)
        return sensitiveFieldService.checkSensitiveFields(lineageTableFieldsVOS);
    }

    @Override
    public PreviewDataResponse previewData(String sql, Long dataSourceId) {
        log.info("预览数据, dataSourceId: {}", dataSourceId);
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取数据源信息
            DataSource dataSource = dataSourceService.getDataSourceEntityById(dataSourceId);
            String databaseType = dataSource.getType();
            
            // 添加LIMIT子句限制预览数据量
            String limitedSql = sqlValidationService.addLimitClause(sql, 100, databaseType);
            
            // 执行查询并获取预览数据
            List<Map<String, Object>> rows = executePreviewQuery(limitedSql, dataSource);
            
            // 提取列名
            List<String> columns = new ArrayList<>();
            if (!rows.isEmpty()) {
                columns.addAll(rows.get(0).keySet());
            }
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            log.info("预览数据成功, 行数: {}, 执行时间: {}ms", rows.size(), executionTime);
            
            return PreviewDataResponse.success(columns, rows, executionTime);
            
        } catch (Exception e) {
            log.error("预览数据失败", e);
            return PreviewDataResponse.fail("数据预览失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Long createDraftTask(TaskCreateRequest request) {
        log.info("创建草稿任务, oaId: {}", request.getOaid());
        
        // 检查OA ID是否已存在
        if (existsByOaId(request.getOaid())) {
            throw new RuntimeException("OA ID已存在: " + request.getOaid());
        }
        
        // 创建任务实体
        ExtractionTask task = new ExtractionTask();
        BeanUtils.copyProperties(request, task);
        task.setExtractionStatus(ExtractionStatus.DRAFT.name());

        
        // 保存任务
        taskMapper.insert(task);
        
        log.info("草稿任务创建成功, taskId: {}", task.getId());
        return task.getId();
    }

    @Override
    @Transactional
    public TaskSubmissionResponse submitTask(TaskSubmissionRequest request) {
        log.info("提交任务到执行队列, oaId: {}", request.getOaid());

        DataSource dataSource = dataSourceService.getDataSourceEntityById(request.getDataSourceId());
        String databaseType = dataSource.getType();
        List<SensitiveFieldCheckResult> sensitiveFieldCheckResults = this.checkSensitiveFields(request.getExtractionScript(), databaseType);

        if (!CollectionUtils.isEmpty(sensitiveFieldCheckResults)) {
            log.warn("涉及敏感字段，需要上传合规审批文件");
            request.setRequiresCompliance(true);
        }

        // 创建任务实体
        ExtractionTask task = new ExtractionTask();
        BeanUtils.copyProperties(request, task);
        
        // 设置任务状态
        if (request.getRequiresCompliance()) {
            task.setExtractionStatus(ExtractionStatus.PENDING_COMPLIANCE_REVIEW.name());
        } else {
            task.setExtractionStatus(ExtractionStatus.PENDING_EXTRACTION.name());
        }
        
        // 保存任务
        taskMapper.insert(task);
        
        // 待提数，则加入执行队列
        if (ExtractionStatus.PENDING_EXTRACTION.name().equals(task.getExtractionStatus())) {
            createExecutionQueueItem(task.getId());
        }
        
        log.info("任务提交成功, taskId: {}, status: {}", task.getId(), task.getExtractionStatus());
        if(request.getRequiresCompliance()){
            return TaskSubmissionResponse.requiresCompliance(sensitiveFieldCheckResults);
        }
        return TaskSubmissionResponse.success();

    }

    @Override
    public boolean existsByOaId(String oaId) {
        return taskMapper.countByOaid(oaId) > 0;
    }

    @Override
    @Transactional
    public void uploadComplianceFile(Long taskId, String fileUrl) {
        log.info("上传合规审批文件, taskId: {}, fileUrl: {}", taskId, fileUrl);
        
        ExtractionTask task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }
        
        if (!task.isEditable()) {
            throw new RuntimeException("任务当前状态不允许上传文件");
        }
        
        task.setCompliancePdfUrl(fileUrl);
        taskMapper.updateById(task);
        
        log.info("合规审批文件上传成功, taskId: {}", taskId);
    }

    @Override
    public String generateSampleData(Long taskId) {
        log.info("生成样例数据文件, taskId: {}", taskId);
        
        ExtractionTask task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }
        
        try {
            // 获取数据源信息
            DataSource dataSource = dataSourceService.getDataSourceEntityById(task.getDataSourceId());
            String databaseType = dataSource.getType();
            
            // 执行SQL获取样例数据(前1000条)
            String sampleSql = sqlValidationService.addLimitClause(task.getExtractionScript(), 1000, databaseType);
            
            List<Map<String, Object>> sampleData = executePreviewQuery(sampleSql, dataSource);
            
            // 生成CSV文件并上传到MinIO
            String sampleFileUrl = generateAndUploadSampleFile(taskId, sampleData);
            
            // 更新任务的样例数据URL
            task.setSampleDataUrl(sampleFileUrl);
            taskMapper.updateById(task);
            
            log.info("样例数据文件生成成功, taskId: {}, fileUrl: {}", taskId, sampleFileUrl);
            return sampleFileUrl;
            
        } catch (Exception e) {
            log.error("生成样例数据文件失败, taskId: {}", taskId, e);
            throw new RuntimeException("生成样例数据文件失败: " + e.getMessage());
        }
    }

    @Override
    public boolean canSubmitTask(Long taskId) {
        ExtractionTask task = taskMapper.selectById(taskId);
        if (task == null) {
            return false;
        }
        
        ExtractionStatus status = task.getExtractionStatusEnum();
        return status == ExtractionStatus.DRAFT || status == ExtractionStatus.DATA_GENERATION_FAILED;
    }




    /**
     * 执行预览查询
     */
    private List<Map<String, Object>> executePreviewQuery(String sql, DataSource dataSource) {
        JdbcTemplate jdbcTemplate = dataSourceManager.getJdbcTemplate(dataSource);
        jdbcTemplate.setMaxRows(100);
        SqlBuilder sqlBuilder = SqlBuilderContext.getSqlBuilderService(dataSource.getType());
        String previewSql = sqlBuilder.buildPreviewSql(sql, 100);

        // todo 脱敏
        return jdbcTemplate.queryForList(previewSql);
    }

    /**
     * 生成并上传样例数据文件
     */
    private String generateAndUploadSampleFile(Long taskId, List<Map<String, Object>> sampleData) {
        // TODO: 实现CSV文件生成和MinIO上传逻辑
        return "sample_" + taskId + ".csv";
    }

    /**
     * 创建执行队列项
     */
    private void createExecutionQueueItem(Long taskId) {
        TaskExecutionQueue queueItem = new TaskExecutionQueue();
        queueItem.setTaskId(taskId);
        queueItem.setQueueStatusEnum(QueueStatus.PENDING);
        queueItem.setPriority(0);
        queueItem.setRetryCount(0);
        queueItem.setMaxRetry(3);
        queueItem.setNextExecuteTime(LocalDateTime.now());
        
        queueMapper.insert(queueItem);
        
        log.info("任务已加入执行队列, taskId: {}", taskId);
    }
}
